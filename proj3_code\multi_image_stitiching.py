import cv2
import numpy as np
import glob
import os
import networkx as nx
from collections import defaultdict

def detect_and_match_features(img1, img2, min_matches=10):
    """
    检测并匹配两幅图像的特征点

    Args:
        img1: 第一幅图像
        img2: 第二幅图像
        min_matches: 最小匹配点数量阈值

    Returns:
        good_matches: 筛选后的良好匹配点
        kp1, kp2: 两幅图像的关键点
    """
    # 创建SIFT检测器
    sift = cv2.SIFT_create()

    # 检测关键点和描述符
    kp1, des1 = sift.detectAndCompute(img1, None)
    kp2, des2 = sift.detectAndCompute(img2, None)

    if des1 is None or des2 is None:
        return None, None, None

    # 使用FLANN匹配器进行特征匹配
    FLANN_INDEX_KDTREE = 1
    index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
    search_params = dict(checks=50)
    flann = cv2.FlannBasedMatcher(index_params, search_params)

    matches = flann.knnMatch(des1, des2, k=2)

    # 使用Lowe's比值测试筛选良好的匹配点
    good_matches = []
    for match_pair in matches:
        if len(match_pair) == 2:
            m, n = match_pair
            if m.distance < 0.7 * n.distance:
                good_matches.append(m)

    # 检查匹配点数量是否足够
    if len(good_matches) < min_matches:
        return None, None, None

    return good_matches, kp1, kp2

def compute_homography_ransac(matches, kp1, kp2):
    """
    使用RANSAC算法计算单应性矩阵

    Args:
        matches: 匹配点列表
        kp1, kp2: 两幅图像的关键点

    Returns:
        homography: 3x3单应性矩阵
        inliers_count: 内点数量
    """
    if len(matches) < 4:
        return None, 0

    # 提取匹配点的坐标
    src_pts = np.float32([kp1[m.queryIdx].pt for m in matches]).reshape(-1, 1, 2)
    dst_pts = np.float32([kp2[m.trainIdx].pt for m in matches]).reshape(-1, 1, 2)

    # 使用RANSAC算法计算单应性矩阵
    homography, mask = cv2.findHomography(src_pts, dst_pts,
                                        cv2.RANSAC,
                                        ransacReprojThreshold=5.0)

    inliers_count = np.sum(mask) if mask is not None else 0

    return homography, inliers_count

def build_matching_graph(images):
    """
    构建图像匹配图

    Args:
        images: 图像列表

    Returns:
        graph: NetworkX图对象，包含图像间的匹配关系
        homographies: 字典，存储图像对之间的单应性矩阵
    """
    n_images = len(images)
    graph = nx.Graph()
    homographies = {}

    # 添加节点
    for i in range(n_images):
        graph.add_node(i)

    print(f"正在构建匹配图，共{n_images}张图像...")

    # 计算所有图像对之间的匹配关系
    for i in range(n_images):
        for j in range(i + 1, n_images):
            print(f"  检查图像 {i} 和 {j} 的匹配关系...")

            matches, kp1, kp2 = detect_and_match_features(images[i], images[j])

            if matches is not None:
                homography, inliers_count = compute_homography_ransac(matches, kp1, kp2)

                if homography is not None and inliers_count >= 20:  # 需要足够的内点
                    # 添加边，权重为内点数量
                    graph.add_edge(i, j, weight=inliers_count)
                    homographies[(i, j)] = homography
                    # 计算逆单应性矩阵
                    homographies[(j, i)] = np.linalg.inv(homography)
                    print(f"    找到有效匹配：{len(matches)}个匹配点，{inliers_count}个内点")
                else:
                    print(f"    匹配质量不足")
            else:
                print(f"    无法找到足够的匹配点")

    return graph, homographies

def find_reference_image(graph):
    """
    找到参考图像（连接数最多的图像）

    Args:
        graph: 匹配图

    Returns:
        reference_idx: 参考图像的索引
    """
    if len(graph.nodes()) == 0:
        return 0

    # 选择度数最大的节点作为参考图像
    degrees = dict(graph.degree())
    reference_idx = max(degrees, key=degrees.get)

    print(f"选择图像 {reference_idx} 作为参考图像（连接数：{degrees[reference_idx]}）")

    return reference_idx

def compute_global_homographies(graph, homographies, reference_idx):
    """
    计算所有图像相对于参考图像的全局单应性矩阵

    Args:
        graph: 匹配图
        homographies: 图像对之间的单应性矩阵
        reference_idx: 参考图像索引

    Returns:
        global_homographies: 字典，存储每个图像相对于参考图像的单应性矩阵
    """
    global_homographies = {}
    global_homographies[reference_idx] = np.eye(3)  # 参考图像的单应性矩阵为单位矩阵

    # 使用BFS遍历图，计算全局单应性矩阵
    visited = set([reference_idx])
    queue = [reference_idx]

    while queue:
        current = queue.pop(0)

        # 遍历当前节点的所有邻居
        for neighbor in graph.neighbors(current):
            if neighbor not in visited:
                # 计算从参考图像到邻居图像的全局单应性矩阵
                if (current, neighbor) in homographies:
                    global_homographies[neighbor] = np.dot(homographies[(current, neighbor)],
                                                         global_homographies[current])
                elif (neighbor, current) in homographies:
                    global_homographies[neighbor] = np.dot(homographies[(neighbor, current)],
                                                         global_homographies[current])

                visited.add(neighbor)
                queue.append(neighbor)

    return global_homographies

def calculate_canvas_size(images, global_homographies, max_dimension=10000):
    """
    计算拼接画布的尺寸

    Args:
        images: 图像列表
        global_homographies: 全局单应性矩阵字典
        max_dimension: 最大画布尺寸限制

    Returns:
        canvas_size: (width, height)
        offset: (x_offset, y_offset)
        scale_factor: 缩放因子
    """
    all_corners = []

    for i, img in enumerate(images):
        if i not in global_homographies:
            continue

        h, w = img.shape[:2]
        corners = np.float32([[0, 0], [w, 0], [w, h], [0, h]]).reshape(-1, 1, 2)

        # 变换角点
        transformed_corners = cv2.perspectiveTransform(corners, global_homographies[i])
        all_corners.extend(transformed_corners.reshape(-1, 2))

    if not all_corners:
        return (1000, 1000), (0, 0), 1.0

    all_corners = np.array(all_corners)
    x_min, y_min = all_corners.min(axis=0)
    x_max, y_max = all_corners.max(axis=0)

    # 计算原始画布尺寸
    original_width = int(x_max - x_min) + 10
    original_height = int(y_max - y_min) + 10

    # 计算缩放因子以限制画布尺寸
    scale_factor = 1.0
    if original_width > max_dimension or original_height > max_dimension:
        scale_factor = min(max_dimension / original_width, max_dimension / original_height)
        print(f"画布尺寸过大，应用缩放因子: {scale_factor:.3f}")

    # 应用缩放
    canvas_width = int(original_width * scale_factor)
    canvas_height = int(original_height * scale_factor)
    x_offset = int(-x_min * scale_factor) + 5
    y_offset = int(-y_min * scale_factor) + 5

    return (canvas_width, canvas_height), (x_offset, y_offset), scale_factor

def stitch_multiple_images(images, global_homographies, canvas_size, offset, scale_factor=1.0):
    """
    将多幅图像拼接到同一画布上

    Args:
        images: 图像列表
        global_homographies: 全局单应性矩阵字典
        canvas_size: 画布尺寸 (width, height)
        offset: 偏移量 (x_offset, y_offset)
        scale_factor: 缩放因子

    Returns:
        stitched_image: 拼接后的图像
    """
    canvas_width, canvas_height = canvas_size
    x_offset, y_offset = offset

    # 创建空白画布
    stitched_image = np.zeros((canvas_height, canvas_width, 3), dtype=np.uint8)

    # 创建缩放和平移矩阵
    scale_matrix = np.array([[scale_factor, 0, 0],
                            [0, scale_factor, 0],
                            [0, 0, 1]], dtype=np.float32)

    translation_matrix = np.array([[1, 0, x_offset],
                                  [0, 1, y_offset],
                                  [0, 0, 1]], dtype=np.float32)

    # 按照连接强度排序，先拼接连接较强的图像
    image_indices = list(global_homographies.keys())

    for i in image_indices:
        if i >= len(images):
            continue

        # 计算最终的变换矩阵（包含缩放和平移）
        final_homography = np.dot(translation_matrix,
                                 np.dot(scale_matrix, global_homographies[i]))

        # 变换图像
        warped_image = cv2.warpPerspective(images[i], final_homography,
                                         (canvas_width, canvas_height))

        # 创建掩码，找到非零像素
        mask = cv2.warpPerspective(np.ones(images[i].shape[:2], dtype=np.uint8) * 255,
                                 final_homography, (canvas_width, canvas_height))

        # 简单的图像融合：用新图像覆盖空白区域
        mask_3channel = cv2.merge([mask, mask, mask])
        mask_bool = mask_3channel > 0

        # 只在画布为空的地方放置新图像
        canvas_empty = np.all(stitched_image == 0, axis=2)
        canvas_empty_3channel = np.stack([canvas_empty, canvas_empty, canvas_empty], axis=2)

        # 组合条件：新图像有内容且画布为空
        update_mask = mask_bool & canvas_empty_3channel

        stitched_image[update_mask] = warped_image[update_mask]

        # 对于重叠区域，使用简单的平均融合
        overlap_mask = mask_bool & ~canvas_empty_3channel
        if np.any(overlap_mask):
            stitched_image[overlap_mask] = (stitched_image[overlap_mask].astype(np.float32) +
                                          warped_image[overlap_mask].astype(np.float32)) / 2
            stitched_image = stitched_image.astype(np.uint8)

    return stitched_image

def stitch_case(case_name, input_dir, output_dir):
    image_paths = sorted(glob.glob(os.path.join(input_dir, case_name, "*")))
    images = [cv2.imread(p) for p in image_paths]
    if any(img is None for img in images) or len(images) < 2:
        print(f"[{case_name}] Skipped: insufficient or unreadable images.")
        return

    print(f"[{case_name}] 开始处理多图像拼接，共{len(images)}张图像...")

    # 步骤1: 构建匹配图
    graph, homographies = build_matching_graph(images)

    # 检查图的连通性
    if len(graph.edges()) == 0:
        print(f"[{case_name}] 无法找到图像间的匹配关系")
        return

    # 检查是否所有图像都连通
    connected_components = list(nx.connected_components(graph))
    if len(connected_components) > 1:
        print(f"[{case_name}] 图像不完全连通，有{len(connected_components)}个连通分量")
        # 选择最大的连通分量
        largest_component = max(connected_components, key=len)
        print(f"[{case_name}] 使用最大连通分量，包含图像: {sorted(largest_component)}")

        # 创建子图
        subgraph = graph.subgraph(largest_component)
        # 过滤图像和单应性矩阵
        filtered_images = [images[i] for i in sorted(largest_component)]
        filtered_homographies = {k: v for k, v in homographies.items()
                               if k[0] in largest_component and k[1] in largest_component}

        # 重新映射索引
        index_mapping = {old_idx: new_idx for new_idx, old_idx in enumerate(sorted(largest_component))}
        new_homographies = {}
        for (i, j), H in filtered_homographies.items():
            new_i, new_j = index_mapping[i], index_mapping[j]
            new_homographies[(new_i, new_j)] = H

        # 创建新图
        new_graph = nx.Graph()
        for i in range(len(filtered_images)):
            new_graph.add_node(i)
        for (i, j), H in new_homographies.items():
            new_graph.add_edge(i, j, weight=graph[list(largest_component)[i]][list(largest_component)[j]]['weight'])

        graph = new_graph
        homographies = new_homographies
        images = filtered_images

    # 步骤2: 找到参考图像
    reference_idx = find_reference_image(graph)

    # 步骤3: 计算全局单应性矩阵
    global_homographies = compute_global_homographies(graph, homographies, reference_idx)

    print(f"[{case_name}] 成功计算了{len(global_homographies)}张图像的全局单应性矩阵")

    # 步骤4: 计算画布尺寸
    canvas_size, offset, scale_factor = calculate_canvas_size(images, global_homographies)
    print(f"[{case_name}] 画布尺寸: {canvas_size}, 偏移: {offset}, 缩放: {scale_factor:.3f}")

    # 步骤5: 拼接图像
    stitched_image = stitch_multiple_images(images, global_homographies, canvas_size, offset, scale_factor)

    case_output_dir = output_dir
    os.makedirs(case_output_dir, exist_ok=True)
    output_path = os.path.join(case_output_dir, f"{case_name}.JPG")
    cv2.imwrite(output_path, stitched_image)
    print(f"[{case_name}] Done: saved to {output_path}")

def main():
    input_root = "data/task2_multiview"
    output_root = "output/task2_multiview"
    os.makedirs(output_root, exist_ok=True)
    cases = [name for name in os.listdir(input_root) if os.path.isdir(os.path.join(input_root, name))]
    if not cases:
        print("No cases found in 'data' directory.")
        return

    for case in sorted(cases):
        stitch_case(case, input_root, output_root)

if __name__ == "__main__":
    main()
