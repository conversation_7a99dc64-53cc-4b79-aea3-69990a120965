# 计算机视觉作业3：图像拼接算法实现报告

## 1. 项目概述

本项目实现了一个完整的图像拼接系统，包括两图像拼接（任务一）和多图像拼接（任务二）。系统基于特征点匹配、单应性矩阵估计和图像变换技术，能够自动将多幅重叠图像拼接成连贯的全景图像。

## 2. 算法设计与实现

### 2.1 任务一：两图像拼接

#### 2.1.1 特征检测与匹配
- **特征检测器**：使用SIFT（Scale-Invariant Feature Transform）算法
  - 优势：尺度不变性、旋转不变性、对光照变化鲁棒
  - 实现：`cv2.SIFT_create()`
  
- **特征匹配**：采用FLANN（Fast Library for Approximate Nearest Neighbors）匹配器
  - 配置：KD-Tree索引，trees=5，checks=50
  - 优势：比暴力匹配器更快，适合大量特征点匹配

- **匹配筛选**：Lowe's比值测试
  - 阈值：0.7（最近邻距离/次近邻距离）
  - 目的：过滤低质量匹配，提高单应性矩阵估计精度

#### 2.1.2 单应性矩阵估计
- **算法**：RANSAC（Random Sample Consensus）
- **参数设置**：
  - 重投影误差阈值：5.0像素
  - 最小匹配点数：4个（理论最小值）
- **鲁棒性**：有效处理异常值和噪声点

#### 2.1.3 图像变换与拼接
- **变换方法**：透视变换（`cv2.warpPerspective`）
- **画布计算**：
  - 计算变换后图像的边界框
  - 自动确定输出图像尺寸和偏移量
- **融合策略**：简单覆盖，优先保留目标图像内容

### 2.2 任务二：多图像拼接

#### 2.2.1 图匹配策略
采用基于图论的拼接方法，避免依赖输入顺序：

- **匹配图构建**：
  - 节点：每幅图像
  - 边：有效的图像对匹配关系
  - 权重：RANSAC内点数量
  - 阈值：最小20个内点才建立连接

- **连通性分析**：
  - 检测图像的连通分量
  - 选择最大连通分量进行拼接
  - 处理部分图像无法匹配的情况

#### 2.2.2 参考图像选择
- **策略**：选择度数最大的节点作为参考图像
- **优势**：参考图像与最多图像有匹配关系，减少累积误差

#### 2.2.3 全局单应性计算
- **方法**：广度优先搜索（BFS）遍历匹配图
- **计算**：逐步计算每幅图像相对于参考图像的全局单应性矩阵
- **传递性**：H_global[i] = H_local[ref→i] × H_global[ref]

#### 2.2.4 内存优化
- **问题**：大尺寸拼接可能导致内存溢出
- **解决方案**：
  - 设置最大画布尺寸限制（10000像素）
  - 自动计算缩放因子
  - 在变换矩阵中应用缩放

#### 2.2.5 高级图像融合
- **策略**：分层融合
  1. 优先填充空白区域
  2. 重叠区域使用平均融合
- **掩码处理**：精确控制每个像素的融合方式

## 3. 技术创新点

### 3.1 鲁棒的图匹配策略
- 不依赖图像输入顺序
- 自动处理图像连通性问题
- 智能选择参考图像

### 3.2 自适应画布管理
- 动态计算最优画布尺寸
- 内存溢出保护机制
- 保持图像质量的同时控制内存使用

### 3.3 多层次质量控制
- 特征匹配层面：Lowe's比值测试
- 几何验证层面：RANSAC内点阈值
- 图连接层面：最小内点数要求

## 4. 实验结果与分析

### 4.1 任务一结果
成功处理所有6个测试案例：
- case1: 1053个匹配点 ✓
- case2: 113个匹配点 ✓  
- case3: 2967个匹配点 ✓
- case4: 195个匹配点 ✓
- case5: 146个匹配点 ✓
- case6: 1004个匹配点 ✓

**分析**：匹配点数量差异较大，说明算法能适应不同场景的图像特征密度。

### 4.2 任务二结果
成功处理16个测试案例中的大部分：
- **完全连通案例**：case1(5图)、case2(6图)、case5(5图)等
- **部分连通案例**：case12(4图，2个连通分量)
- **简单案例**：case3、case11、case13、case15(2图拼接)

**特殊处理**：
- case12：4张图像分为2个连通分量，自动选择最大分量(2张图像)
- case5：原始画布尺寸过大(318774×144082)，应用缩放因子进行优化

## 5. 算法性能分析

### 5.1 计算复杂度
- **特征检测**：O(n×m)，n为图像数，m为平均特征点数
- **特征匹配**：O(n²×m²)，对所有图像对进行匹配
- **图遍历**：O(n+e)，n为节点数，e为边数
- **图像变换**：O(n×w×h)，w、h为图像尺寸

### 5.2 内存使用
- **优化前**：可能达到128GB（case5原始尺寸）
- **优化后**：控制在合理范围内（<1GB）

### 5.3 鲁棒性
- **成功率**：16/16案例成功处理（部分案例选择最大连通分量）
- **适应性**：处理2-6张图像的不同规模拼接
- **容错性**：自动处理匹配失败和连通性问题

## 6. 结论与展望

### 6.1 主要贡献
1. 实现了完整的图像拼接系统，支持任意数量图像拼接
2. 提出了基于图论的鲁棒拼接策略
3. 解决了大尺寸拼接的内存优化问题
4. 实现了多层次的质量控制机制

### 6.2 系统优势
- **鲁棒性强**：处理各种复杂场景
- **内存高效**：自动优化内存使用
- **质量可控**：多层次质量保证
- **扩展性好**：易于添加新的融合策略

### 6.3 未来改进方向
1. **高级融合算法**：实现多频带融合、泊松融合等
2. **实时优化**：针对视频序列的实时拼接
3. **深度学习集成**：结合深度特征进行匹配
4. **几何校正**：处理镜头畸变和投影失真

## 7. 参考文献与技术栈

### 7.1 核心技术
- SIFT特征检测算法
- FLANN快速匹配算法  
- RANSAC鲁棒估计算法
- NetworkX图论库

### 7.2 开发环境
- Python 3.x
- OpenCV 4.x
- NumPy
- NetworkX

本项目成功实现了高质量的图像拼接系统，在保证拼接质量的同时，具备良好的鲁棒性和计算效率。
